/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 工具栏样式 */
.toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.toolbar h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.toolbar-right {
    display: flex;
    gap: 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background-color: #4f46e5;
    color: white;
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 组件面板 */
.components-panel {
    width: 280px;
    background: white;
    border-right: 1px solid #e5e7eb;
    padding: 1.5rem;
    overflow-y: auto;
}

.components-panel h3 {
    color: #374151;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.component-category {
    margin-bottom: 2rem;
}

.component-category h4 {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.component-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    margin-bottom: 0.75rem;
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: grab;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.component-item:hover {
    background: #ffffff;
    border-color: #4f46e5;
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.15);
}

.component-item:active {
    cursor: grabbing;
    transform: translateY(0);
}

.component-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
}

.component-info {
    flex: 1;
    min-width: 0;
}

.component-name {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.component-desc {
    color: #6b7280;
    font-size: 0.8rem;
    line-height: 1.4;
    word-wrap: break-word;
}

/* 设计区域 */
.design-area {
    flex: 1;
    background: #f9fafb;
    padding: 2rem;
    overflow: auto;
}

.design-canvas {
    background: white;
    min-height: 600px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: relative;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 0;
    transition: all 0.3s ease;
}

.design-canvas.drag-over {
    background-color: rgba(79, 70, 229, 0.05);
    border: 2px dashed #4f46e5;
    box-shadow: 0 4px 20px rgba(79, 70, 229, 0.2);
}

.canvas-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #9ca3af;
}

.canvas-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.canvas-placeholder p {
    font-size: 1.1rem;
}

/* 拖拽指示器样式 */
.drop-indicator {
    position: relative;
    height: 50px;
    margin: 0.75rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    animation: fadeIn 0.2s ease;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.15) 0%, rgba(139, 92, 246, 0.15) 100%);
    border: 3px dashed #4f46e5;
    border-radius: 12px;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

.drop-line {
    position: absolute;
    top: 50%;
    left: 10px;
    right: 10px;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5, #8b5cf6, #4f46e5);
    transform: translateY(-50%);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(79, 70, 229, 0.5);
}

.drop-text {
    background: white;
    color: #4f46e5;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 600;
    z-index: 1;
    border: 2px solid #4f46e5;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

/* 拖拽区域样式 */
.drop-zone {
    min-height: 100px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
    transition: all 0.3s ease;
    position: relative;
    padding: 1rem;
}

.drop-zone.drag-over {
    border-color: #4f46e5;
    background-color: #eef2ff;
    color: #4f46e5;
    transform: scale(1.02);
}

.drop-zone:empty::before {
    content: "拖拽组件到这里";
    font-size: 0.9rem;
    color: #9ca3af;
    pointer-events: none;
}

.drop-zone.drag-over:empty::before {
    content: "释放以添加组件";
    color: #4f46e5;
}

/* 嵌套元素样式 */
.nested-element {
    margin: 0.5rem 0;
    border-left: 3px solid #e5e7eb;
    padding-left: 1rem;
}

.nested-element.selected {
    border-left-color: #4f46e5;
}

/* 容器占位符样式 */
.container-placeholder,
.row-placeholder,
.column-placeholder {
    padding: 2rem;
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    background-color: #f9fafb;
    transition: all 0.3s ease;
}

.container-placeholder:hover,
.row-placeholder:hover,
.column-placeholder:hover {
    border-color: #9ca3af;
    background-color: #f3f4f6;
}

/* 设计元素样式 */
.design-element {
    position: relative;
    margin: 1rem 0;
    padding: 0.75rem;
    border: 2px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.8);
    min-height: 40px;
    display: block;
    clear: both;
}

.design-element:hover {
    border-color: #d1d5db;
    background-color: rgba(249, 250, 251, 0.8);
}

.design-element.selected {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    background-color: rgba(238, 242, 255, 0.3);
}

.design-element[draggable="true"] {
    cursor: grab;
}

.design-element[draggable="true"]:active {
    cursor: grabbing;
}

.design-element .element-controls {
    position: absolute;
    top: -30px;
    right: 0;
    display: none;
    gap: 0.25rem;
}

.design-element:hover .element-controls,
.design-element.selected .element-controls {
    display: flex;
}

.element-control-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.element-control-btn.delete {
    background-color: #ef4444;
    color: white;
}

.element-control-btn.duplicate {
    background-color: #10b981;
    color: white;
}

/* 新组件样式 */
.tabs-container {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.tab-headers {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.tab-header {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-right: 1px solid #ddd;
    transition: background-color 0.3s ease;
}

.tab-header:last-child {
    border-right: none;
}

.tab-header:hover {
    background: #e9ecef;
}

.tab-header.active {
    background: white;
    border-bottom: 2px solid #4f46e5;
}

.collapsible {
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
}

.collapsible-header {
    padding: 1rem;
    background: #f8f9fa;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.3s ease;
}

.collapsible-header:hover {
    background: #e9ecef;
}

.collapsible-content {
    padding: 1rem;
    border-top: 1px solid #ddd;
}

.gallery {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
}

.gallery-title {
    font-weight: bold;
    margin-bottom: 1rem;
    text-align: center;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.gallery-item {
    text-align: center;
}

.gallery-caption {
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.notice {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.notice-info {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.notice-warning {
    background: #fff3e0;
    border-left: 4px solid #ff9800;
}

.notice-error {
    background: #ffebee;
    border-left: 4px solid #f44336;
}

.notice-success {
    background: #e8f5e8;
    border-left: 4px solid #4caf50;
}

.notice-icon {
    font-size: 1.2rem;
    margin-top: 0.2rem;
}

.notice-title {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.category-tag {
    background: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 0.5rem;
    font-family: monospace;
    color: #666;
}

/* MediaWiki 真实样式 */
.infobox {
    border: 1px solid #a2a9b1;
    border-spacing: 3px;
    background-color: #f8f9fa;
    color: black;
    margin: 0.5em 0 0.5em 1em;
    padding: 0.2em;
    float: right;
    clear: right;
    font-size: 88%;
    line-height: 1.5em;
    width: 22em;
    border-collapse: collapse;
}

.infobox-title {
    font-size: 125%;
    font-weight: bold;
    text-align: center;
    background-color: #ccccff;
    padding: 0.2em;
}

.infobox-image {
    text-align: center;
    padding: 0.2em;
}

.infobox-content {
    padding: 0.2em;
}

.infobox-row {
    display: table-row;
}

.infobox-row .label {
    display: table-cell;
    padding: 0.2em 0.4em;
    vertical-align: top;
    font-weight: bold;
    background-color: #eaecf0;
    width: 30%;
}

.infobox-row .value {
    display: table-cell;
    padding: 0.2em 0.4em;
    vertical-align: top;
}

.navbox {
    border: 1px solid #a2a9b1;
    width: 100%;
    clear: both;
    font-size: 88%;
    text-align: center;
    padding: 1px;
    margin: 1em auto 0;
    background-color: #f8f9fa;
    border-collapse: collapse;
}

.navbox-title {
    background-color: #ccccff;
    font-size: 110%;
    font-weight: bold;
    padding: 0.25em;
}

.navbox-content {
    background-color: #f8f9fa;
}

.navbox-group {
    padding: 0.25em;
    border-top: 1px solid #a2a9b1;
}

.navbox-group:first-child {
    border-top: none;
}

.navbox-group-title {
    font-weight: bold;
    background-color: #eaecf0;
    padding: 0.25em 0.5em;
    margin-bottom: 0.25em;
}

.navbox-links {
    padding: 0.25em;
}

.navbox-links a {
    color: #0645ad;
    text-decoration: none;
}

.navbox-links a:hover {
    text-decoration: underline;
}

.wikitable {
    background-color: #f8f9fa;
    color: black;
    margin: 1em 0;
    border: 1px solid #a2a9b1;
    border-collapse: collapse;
}

.wikitable th,
.wikitable td {
    border: 1px solid #a2a9b1;
    padding: 0.2em 0.4em;
}

.wikitable th {
    background-color: #eaecf0;
    text-align: center;
    font-weight: bold;
}

.wikitable tr:nth-child(even) {
    background-color: #f8f9fa;
}

.wikitable tr:nth-child(odd) {
    background-color: white;
}

/* 模板参数样式 */
.template-param {
    background-color: #ffffcc;
    border: 1px solid #ffcc00;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
    color: #333;
}

/* 图片占位符样式 */
.image-placeholder {
    background-color: #f8f9fa;
    border: 1px solid #a2a9b1;
    padding: 2rem;
    text-align: center;
    color: #72777d;
    font-size: 0.9em;
    border-radius: 4px;
}

.image-placeholder i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* 标签页样式改进 */
.tabs-container {
    border: 1px solid #a2a9b1;
    background-color: #f8f9fa;
}

.tab-headers {
    display: flex;
    background-color: #eaecf0;
    border-bottom: 1px solid #a2a9b1;
}

.tab-header {
    padding: 0.5rem 1rem;
    border-right: 1px solid #a2a9b1;
    cursor: pointer;
    background-color: #eaecf0;
    color: #0645ad;
    font-weight: normal;
}

.tab-header.active {
    background-color: #f8f9fa;
    border-bottom: 1px solid #f8f9fa;
    margin-bottom: -1px;
    font-weight: bold;
}

.tab-content {
    padding: 1rem;
    background-color: #f8f9fa;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 可折叠内容样式改进 */
.collapsible {
    border: 1px solid #a2a9b1;
    background-color: #f8f9fa;
}

.collapsible-header {
    background-color: #eaecf0;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #a2a9b1;
    cursor: pointer;
    font-weight: bold;
}

.collapsible-content {
    padding: 1rem;
    background-color: #f8f9fa;
}

/* 画廊样式改进 */
.gallery {
    border: 1px solid #a2a9b1;
    background-color: #f8f9fa;
    padding: 1rem;
    margin: 1rem 0;
}

.gallery-title {
    font-weight: bold;
    text-align: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background-color: #eaecf0;
    border-bottom: 1px solid #a2a9b1;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    padding: 0.5rem;
}

.gallery-item {
    text-align: center;
    border: 1px solid #a2a9b1;
    padding: 0.5rem;
    background-color: white;
}

.gallery-caption {
    margin-top: 0.5rem;
    font-size: 0.85em;
    color: #72777d;
    font-style: italic;
}

/* 属性面板 */
.properties-panel {
    width: 360px;
    background: white;
    border-left: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
}

.panel-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 0.9rem;
    color: #6b7280;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    background-color: #f9fafb;
}

.tab-btn.active {
    color: #4f46e5;
    border-bottom: 2px solid #4f46e5;
    background-color: #eef2ff;
}

.tab-content {
    flex: 1;
    overflow-y: auto;
}

.tab-pane {
    display: none;
    padding: 1.5rem;
}

.tab-pane.active {
    display: block;
}

.no-selection {
    text-align: center;
    color: #9ca3af;
    padding: 3rem 1rem;
}

.no-selection i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}

/* 属性编辑器样式 */
.property-header {
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
}

.property-header h4 {
    color: #374151;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.property-header small {
    color: #6b7280;
    font-size: 0.8rem;
}

.property-row {
    margin-bottom: 1.5rem;
}

.property-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.property-label small {
    display: block;
    font-weight: normal;
    color: #6b7280;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.property-row input,
.property-row textarea,
.property-row select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background-color: white;
}

.property-row input:focus,
.property-row textarea:focus,
.property-row select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.property-row textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

.property-row select {
    cursor: pointer;
}

/* 样式编辑器 */
.style-group {
    margin-bottom: 2rem;
}

.style-group h4 {
    color: #374151;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.style-row {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.style-row label {
    flex: 1;
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.style-row input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.9rem;
}

.style-row input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.style-row select {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
    cursor: pointer;
}

.style-row select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.style-row input[type="color"] {
    width: 50px;
    height: 38px;
    padding: 0;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    cursor: pointer;
}

.unit-label {
    margin-left: 8px;
    color: #6b7280;
    font-size: 0.8rem;
    font-weight: normal;
    min-width: 20px;
}

/* 内联编辑样式 */
.inline-editing {
    outline: 2px solid #4f46e5 !important;
    outline-offset: 2px;
    background: rgba(79, 70, 229, 0.05) !important;
}

.inline-editor {
    width: 100% !important;
    padding: 8px !important;
    border: 2px solid #4f46e5 !important;
    border-radius: 4px !important;
    font-size: inherit !important;
    font-family: inherit !important;
    background: white !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    z-index: 1000 !important;
    position: relative !important;
}

.inline-editor:focus {
    outline: none !important;
    border-color: #6366f1 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(79, 70, 229, 0.2) !important;
}

/* 调整大小样式 */
.resize-handles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

.resize-handle {
    position: absolute;
    background: #4f46e5;
    border: 1px solid white;
    border-radius: 2px;
    pointer-events: all;
    opacity: 0;
    transition: opacity 0.2s;
}

.design-element.selected .resize-handle {
    opacity: 1;
}

.resize-handle:hover {
    background: #6366f1;
    transform: scale(1.2);
}

.resize-handle-nw, .resize-handle-se {
    width: 8px;
    height: 8px;
    cursor: nw-resize;
}

.resize-handle-ne, .resize-handle-sw {
    width: 8px;
    height: 8px;
    cursor: ne-resize;
}

.resize-handle-n, .resize-handle-s {
    width: 20px;
    height: 6px;
    cursor: n-resize;
    left: 50%;
    transform: translateX(-50%);
}

.resize-handle-w, .resize-handle-e {
    width: 6px;
    height: 20px;
    cursor: w-resize;
    top: 50%;
    transform: translateY(-50%);
}

.resize-handle-nw { top: -4px; left: -4px; }
.resize-handle-n { top: -3px; }
.resize-handle-ne { top: -4px; right: -4px; }
.resize-handle-w { left: -3px; }
.resize-handle-e { right: -3px; }
.resize-handle-sw { bottom: -4px; left: -4px; }
.resize-handle-s { bottom: -3px; }
.resize-handle-se { bottom: -4px; right: -4px; }

.resize-tooltip {
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: monospace;
    z-index: 10000;
    pointer-events: none;
}

/* 代码区域 */
.code-section {
    margin-bottom: 2rem;
}

.code-section h4 {
    color: #374151;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.code-section textarea {
    width: 100%;
    height: 200px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    resize: vertical;
    background-color: #f9fafb;
}

.code-section textarea:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.close {
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    color: white;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 1;
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

.export-section {
    margin-bottom: 2rem;
}

.export-section h4 {
    color: #374151;
    margin-bottom: 1rem;
}

.export-section textarea {
    width: 100%;
    height: 150px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    margin-bottom: 1rem;
    background-color: #f9fafb;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .components-panel {
        width: 240px;
    }
    
    .properties-panel {
        width: 280px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .components-panel,
    .properties-panel {
        width: 100%;
        height: 200px;
    }
    
    .toolbar {
        padding: 1rem;
    }
    
    .toolbar h1 {
        font-size: 1.2rem;
    }
    
    .toolbar-right {
        gap: 0.5rem;
    }
    
    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes dropPulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
    }
    50% {
        opacity: 1;
        transform: scale(1.02);
        box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
    }
}

.drop-indicator {
    animation: fadeIn 0.2s ease, dropPulse 1.5s infinite;
}

.design-element {
    animation: fadeIn 0.3s ease;
}

/* 拖拽提示样式 */
.drag-hint {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.hint-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    text-align: left;
}

.hint-content i {
    color: #4f46e5;
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.hint-content p {
    margin: 0.5rem 0;
    font-weight: 600;
    color: #374151;
}

.hint-content ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
    color: #6b7280;
}

.hint-content li {
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.hint-content button {
    background: #4f46e5;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 1rem;
    transition: background-color 0.3s ease;
}

.hint-content button:hover {
    background: #4338ca;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* AI 助手样式 */
.btn-info {
    background-color: #06b6d4;
    color: white;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.ai-modal {
    max-width: 800px;
    height: 600px;
}

.ai-chat-container {
    display: flex;
    flex-direction: column;
    height: 500px;
}

.ai-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.ai-message {
    display: flex;
    margin-bottom: 1rem;
    animation: fadeIn 0.3s ease;
}

.ai-message-user {
    justify-content: flex-end;
}

.ai-message-assistant {
    justify-content: flex-start;
}

.ai-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 0.5rem;
    font-size: 1.2rem;
}

.ai-message-user .ai-avatar {
    background: #4f46e5;
    color: white;
}

.ai-message-assistant .ai-avatar {
    background: #10b981;
    color: white;
}

.ai-content {
    max-width: 70%;
    padding: 1rem;
    border-radius: 12px;
    line-height: 1.5;
}

.ai-message-user .ai-content {
    background: #4f46e5;
    color: white;
}

.ai-message-assistant .ai-content {
    background: white;
    border: 1px solid #e5e7eb;
}

.ai-input-container {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
}

.ai-input-container textarea {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    resize: vertical;
    font-family: inherit;
}

.ai-input-container button {
    padding: 0.75rem 1.5rem;
    white-space: nowrap;
}

.ai-input-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.ai-input-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.ai-image-preview {
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.ai-image-preview img {
    max-width: 100px;
    max-height: 100px;
    border-radius: 4px;
    object-fit: cover;
}

/* AI 配置样式 */
.config-section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
}

.config-section h4 {
    margin-bottom: 1rem;
    color: #374151;
    font-size: 1.1rem;
}

.config-row {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
}

.config-row label {
    flex: 0 0 150px;
    font-weight: 500;
    color: #374151;
}

.config-row input,
.config-row select {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 0.9rem;
}

.config-row input[type="range"] {
    flex: 0 0 150px;
}

.config-row input[type="checkbox"] {
    flex: none;
    width: auto;
}

.config-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.config-help {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
}

.config-help small {
    color: #0369a1;
    line-height: 1.4;
}

/* 内联编辑样式 */
.design-element.inline-editing {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2) !important;
    background-color: rgba(236, 253, 245, 0.5) !important;
}

.inline-editor {
    width: 100% !important;
    padding: 0.5rem !important;
    border: 2px solid #10b981 !important;
    border-radius: 4px !important;
    font-size: inherit !important;
    font-family: inherit !important;
    background: white !important;
    resize: vertical !important;
    min-height: 2rem !important;
}

.inline-editor:focus {
    outline: none !important;
    border-color: #059669 !important;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3) !important;
}

/* 增强的属性面板样式 */
.select-container {
    position: relative;
}

.property-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: white;
    cursor: pointer;
}

.property-select option {
    padding: 0.5rem;
    font-size: 0.9rem;
}

.custom-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #4f46e5;
    border-radius: 6px;
    font-size: 0.9rem;
    background-color: #eef2ff;
}

.custom-input:focus {
    outline: none;
    border-color: #4338ca;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 属性标签增强 */
.property-label {
    position: relative;
}

.property-label .help-icon {
    margin-left: 0.5rem;
    color: #6b7280;
    cursor: help;
    font-size: 0.8rem;
}

.property-label .help-icon:hover {
    color: #4f46e5;
}

/* 属性分组样式 */
.property-group {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
}

.property-group h5 {
    margin-bottom: 1rem;
    color: #374151;
    font-size: 0.95rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.property-group h5 i {
    color: #6b7280;
}

/* 调整大小手柄样式 */
.resize-handles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    display: none;
}

.design-element.selected .resize-handles {
    display: block;
}

.resize-handle {
    position: absolute;
    background: #4f46e5;
    border: 2px solid white;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    pointer-events: all;
    cursor: pointer;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.resize-handle:hover {
    background: #4338ca;
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 调整大小手柄位置 */
.resize-handle-nw {
    top: -6px;
    left: -6px;
    cursor: nw-resize;
}

.resize-handle-n {
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    cursor: n-resize;
}

.resize-handle-ne {
    top: -6px;
    right: -6px;
    cursor: ne-resize;
}

.resize-handle-w {
    top: 50%;
    left: -6px;
    transform: translateY(-50%);
    cursor: w-resize;
}

.resize-handle-e {
    top: 50%;
    right: -6px;
    transform: translateY(-50%);
    cursor: e-resize;
}

.resize-handle-sw {
    bottom: -6px;
    left: -6px;
    cursor: sw-resize;
}

.resize-handle-s {
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    cursor: s-resize;
}

.resize-handle-se {
    bottom: -6px;
    right: -6px;
    cursor: se-resize;
}

/* 调整大小状态样式 */
.design-element.resizing {
    border-color: #4f46e5 !important;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.3) !important;
}

body.resizing {
    cursor: grabbing !important;
    user-select: none;
}

body.resizing * {
    cursor: inherit !important;
}

/* 尺寸提示样式 */
.resize-tooltip {
    position: fixed;
    background: #1f2937;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: none;
}

.resize-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #1f2937;
}

/* 网格对齐辅助线 */
.resize-grid {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 5;
    opacity: 0.3;
    background-image:
        linear-gradient(to right, #e5e7eb 1px, transparent 1px),
        linear-gradient(to bottom, #e5e7eb 1px, transparent 1px);
    background-size: 20px 20px;
    display: none;
}

body.resizing .resize-grid {
    display: block;
}

/* 属性帮助文本样式 */
.help-text {
    display: block;
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: normal;
    margin-top: 0.25rem;
    line-height: 1.3;
}

/* 示例面板样式 */
.examples-section,
.tutorial-section {
    margin-bottom: 2rem;
}

.example-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.example-item:hover {
    background: #f3f4f6;
    border-color: #4f46e5;
    transform: translateY(-1px);
}

.example-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #4f46e5;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.example-info {
    flex: 1;
}

.example-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.example-desc {
    font-size: 0.9rem;
    color: #6b7280;
}

.tutorial-item {
    background: #f8fafc;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.tutorial-item h5 {
    color: #1f2937;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tutorial-item ol,
.tutorial-item ul {
    margin: 0;
    padding-left: 1.5rem;
}

.tutorial-item li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.tutorial-item code {
    background: #e5e7eb;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}
