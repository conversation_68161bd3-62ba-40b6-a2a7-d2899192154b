# MediaWiki 模板可视化设计器

一个专为 MediaWiki 模板设计的可视化工具，让您能够通过拖拽操作轻松创建模板，并自动生成相应的 MediaWiki 代码和 CSS 样式。

## 功能特性

### 🎨 可视化设计
- **拖拽式界面** - 从组件库拖拽元素到设计画布
- **实时预览** - 即时查看设计效果
- **所见即所得** - 直观的设计体验

### 🧩 丰富的组件库
- **基础组件**: 文本、标题、图片、链接
- **布局组件**: 容器、行、列、表格
- **MediaWiki 特殊组件**: 信息框、导航框、模板参数

### ⚙️ 强大的编辑功能
- **属性编辑** - 修改元素的各种属性
- **样式编辑** - 自定义 CSS 样式
- **实时更新** - 修改即时生效

### 📤 代码导出
- **MediaWiki 模板代码** - 标准的 MediaWiki 语法
- **CSS 样式代码** - 完整的样式定义
- **一键复制** - 方便粘贴到 MediaWiki

## 使用方法

### 1. 启动工具
直接在浏览器中打开 `index.html` 文件即可开始使用。

### 2. 设计模板
1. **添加组件**: 从左侧组件库拖拽元素到中间的设计画布
2. **编辑属性**: 点击选中元素，在右侧属性面板中修改属性
3. **调整样式**: 切换到样式标签页，自定义 CSS 样式
4. **预览效果**: 点击工具栏的"预览"按钮查看最终效果

### 3. 导出代码
1. 点击工具栏的"导出代码"按钮
2. 复制生成的 MediaWiki 模板代码
3. 复制生成的 CSS 样式代码
4. 在 MediaWiki 中创建新模板并粘贴代码

## 组件说明

### 基础组件

#### 文本
- 用于添加普通文本内容
- 可设置文本内容和 HTML 标签类型

#### 标题
- 创建不同级别的标题
- 支持 H1-H6 标题级别

#### 图片
- 插入图片元素
- 支持设置图片源、替代文本、宽度和高度

#### 链接
- 创建内部或外部链接
- 支持设置链接地址、显示文本和打开方式

### 布局组件

#### 容器
- 用于包装其他元素
- 提供基本的布局容器

#### 行
- 创建水平布局
- 支持 Flexbox 布局

#### 列
- 创建列布局
- 可设置列宽度

#### 表格
- 创建数据表格
- 支持设置行数、列数和边框

### MediaWiki 特殊组件

#### 信息框 (Infobox)
- 创建标准的 MediaWiki 信息框
- 适用于人物、地点、事件等信息展示

#### 导航框 (Navbox)
- 创建导航模板
- 用于页面底部的相关链接导航

#### 模板参数
- 插入模板参数占位符
- 支持设置参数名和默认值

## 样式编辑

### 布局样式
- **宽度/高度**: 设置元素尺寸
- **边距/内边距**: 控制元素间距

### 文本样式
- **字体大小**: 设置文字大小
- **字体颜色**: 选择文字颜色
- **背景色**: 设置背景颜色

### 边框样式
- **边框**: 设置边框样式
- **圆角**: 设置边框圆角

## 代码生成

### MediaWiki 模板代码
工具会自动生成标准的 MediaWiki 模板语法：
- 使用 `<includeonly>` 和 `<noinclude>` 标签
- 自动添加模板参数说明
- 生成符合 MediaWiki 规范的代码

### CSS 样式代码
- 为每个元素生成对应的 CSS 类
- 包含响应式设计样式
- 提供通用的 MediaWiki 样式

## 技术特性

- **纯前端实现** - 无需服务器，本地即可运行
- **响应式设计** - 支持不同屏幕尺寸
- **现代浏览器支持** - 使用最新的 Web 技术
- **无依赖** - 除了 Font Awesome 图标外无其他外部依赖

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 使用技巧

1. **组件复制**: 选中元素后点击复制按钮可快速复制元素
2. **批量操作**: 使用清空按钮可一次性清除所有元素
3. **代码预览**: 在代码标签页中可实时查看生成的代码
4. **样式调试**: 修改样式后立即在设计画布中看到效果

## 注意事项

- 生成的代码需要在 MediaWiki 环境中测试
- 某些高级 MediaWiki 功能可能需要手动调整代码
- 建议在使用前备份现有的模板代码
- CSS 样式需要添加到 MediaWiki 的样式表中

## 更新日志

### v1.0.0
- 初始版本发布
- 基础组件库
- 可视化设计功能
- 代码生成和导出功能

## 支持与反馈

如果您在使用过程中遇到问题或有改进建议，欢迎提出反馈。

---

**开始使用 MediaWiki 模板可视化设计器，让模板设计变得简单高效！**
